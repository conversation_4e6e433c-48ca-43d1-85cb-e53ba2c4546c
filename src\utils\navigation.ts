/**
 * Navigation utilities for handling hash navigation and smooth scrolling
 */

/**
 * Navigate to a hash section with proper scrolling behavior
 * Handles both same-page and cross-page navigation
 */
export function navigateToSection(hash: string, offset: number = 100) {
  const targetId = hash.replace("#", "");

  // If we're already on the home page, just scroll to the section
  if (window.location.pathname === "/") {
    scrollToSection(targetId, offset);
    return;
  }

  // If we're on a different page, navigate to home and then scroll
  // Store the target section in sessionStorage for after navigation
  sessionStorage.setItem("scrollToSection", targetId);
  sessionStorage.setItem("scrollOffset", offset.toString());

  // Navigate to home page with hash
  window.location.href = `/#${targetId}`;
}

/**
 * Scroll to a specific section by ID
 */
export function scrollToSection(sectionId: string, offset: number = 100) {
  const element = document.getElementById(sectionId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: "smooth",
    });

    // Update URL hash without triggering navigation
    history.replaceState(null, "", `#${sectionId}`);
  }
}

/**
 * Handle hash navigation on page load
 * Should be called when the page loads to handle hash navigation from other pages
 */
export function handleHashNavigation() {
  // Check if we have a stored section to scroll to
  const storedSection = sessionStorage.getItem("scrollToSection");
  const storedOffset = sessionStorage.getItem("scrollOffset");

  if (storedSection) {
    // Clear the stored values
    sessionStorage.removeItem("scrollToSection");
    sessionStorage.removeItem("scrollOffset");

    const offset = storedOffset ? parseInt(storedOffset, 10) : 100;

    // Wait for page to fully load, then scroll with retry logic
    setTimeout(() => {
      const attemptScroll = (attempts = 0) => {
        const element = document.getElementById(storedSection);

        if (element) {
          scrollToSection(storedSection, offset);
        } else if (attempts < 20) {
          // Element not found, try again after a short delay (max 20 attempts = 4 seconds)
          setTimeout(() => attemptScroll(attempts + 1), 200);
        }
      };
      attemptScroll();
    }, 1000);

    return;
  }

  // Check if there's a hash in the current URL
  const hash = window.location.hash;
  if (hash) {
    const sectionId = hash.replace("#", "");
    // Wait for page to fully load, then scroll with retry logic
    setTimeout(() => {
      const attemptScroll = (attempts = 0) => {
        const element = document.getElementById(sectionId);
        if (element) {
          scrollToSection(sectionId, 100);
        } else if (attempts < 20) {
          // Element not found, try again after a short delay (max 20 attempts = 4 seconds)
          setTimeout(() => attemptScroll(attempts + 1), 200);
        }
      };
      attemptScroll();
    }, 1000);
  }
}

/**
 * Initialize hash navigation listeners
 * Should be called once when the app starts
 */
export function initializeHashNavigation() {
  // Handle hash changes
  window.addEventListener("hashchange", () => {
    const hash = window.location.hash;
    if (hash) {
      const sectionId = hash.replace("#", "");
      scrollToSection(sectionId, 100);
    }
  });

  // Handle initial page load
  handleHashNavigation();
}
