import { useState, useEffect, useCallback } from 'react';

export interface CursorState {
  isVisible: boolean;
  isHovering: boolean;
  position: { x: number; y: number };
  isMobile: boolean;
}

export function useCursor() {
  const [cursorState, setCursorState] = useState<CursorState>({
    isVisible: false,
    isHovering: false,
    position: { x: 0, y: 0 },
    isMobile: false,
  });

  const updatePosition = useCallback((x: number, y: number) => {
    setCursorState(prev => ({
      ...prev,
      position: { x, y },
      isVisible: true,
    }));
  }, []);

  const setHovering = useCallback((hovering: boolean) => {
    setCursorState(prev => ({
      ...prev,
      isHovering: hovering,
    }));
  }, []);

  const setVisible = useCallback((visible: boolean) => {
    setCursorState(prev => ({
      ...prev,
      isVisible: visible,
    }));
  }, []);

  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => {
      const isMobile = window.matchMedia('(hover: none) and (pointer: coarse)').matches;
      setCursorState(prev => ({
        ...prev,
        isMobile,
      }));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return {
    cursorState,
    updatePosition,
    setHovering,
    setVisible,
  };
}
