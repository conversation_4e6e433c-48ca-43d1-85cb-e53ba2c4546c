/**
 * Cookie Management Utilities
 * Handles cookie consent, preferences, and GDPR compliance
 */

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export interface CookieConsentData {
  hasConsented: boolean;
  consentDate: string;
  preferences: CookiePreferences;
  version: string;
}

// Cookie consent storage key
const CONSENT_STORAGE_KEY = "conalys-cookie-consent";
const CONSENT_VERSION = "1.0";

// Default preferences (only necessary cookies enabled by default)
export const DEFAULT_PREFERENCES: CookiePreferences = {
  necessary: true, // Always true, cannot be disabled
  analytics: false,
  marketing: false,
  functional: false,
};

/**
 * Get current cookie consent status
 */
export function getCookieConsent(): CookieConsentData | null {
  try {
    const stored = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (!stored) return null;

    const data = JSON.parse(stored) as CookieConsentData;

    // Check if consent version is current
    if (data.version !== CONSENT_VERSION) {
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error reading cookie consent:", error);
    return null;
  }
}

/**
 * Save cookie consent preferences
 */
export function saveCookieConsent(preferences: CookiePreferences): void {
  try {
    const consentData: CookieConsentData = {
      hasConsented: true,
      consentDate: new Date().toISOString(),
      preferences: {
        ...preferences,
        necessary: true, // Always ensure necessary cookies are enabled
      },
      version: CONSENT_VERSION,
    };

    localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData));

    // Apply cookie preferences
    applyCookiePreferences(consentData.preferences);
  } catch (error) {
    console.error("Error saving cookie consent:", error);
  }
}

/**
 * Check if user has given consent
 */
export function hasUserConsented(): boolean {
  const consent = getCookieConsent();
  return consent?.hasConsented ?? false;
}

/**
 * Get current cookie preferences
 */
export function getCookiePreferences(): CookiePreferences {
  const consent = getCookieConsent();
  return consent?.preferences ?? DEFAULT_PREFERENCES;
}

/**
 * Accept all cookies
 */
export function acceptAllCookies(): void {
  const allAcceptedPreferences: CookiePreferences = {
    necessary: true,
    analytics: true,
    marketing: true,
    functional: true,
  };

  saveCookieConsent(allAcceptedPreferences);
}

/**
 * Accept only necessary cookies
 */
export function acceptNecessaryCookies(): void {
  saveCookieConsent(DEFAULT_PREFERENCES);
}

/**
 * Clear all cookie consent data
 */
export function clearCookieConsent(): void {
  try {
    localStorage.removeItem(CONSENT_STORAGE_KEY);
    // Clear all non-necessary cookies
    clearNonNecessaryCookies();
  } catch (error) {
    console.error("Error clearing cookie consent:", error);
  }
}

/**
 * Apply cookie preferences (enable/disable tracking scripts)
 */
function applyCookiePreferences(preferences: CookiePreferences): void {
  // Analytics cookies (Google Analytics, etc.)
  if (preferences.analytics) {
    enableAnalytics();
  } else {
    disableAnalytics();
  }

  // Marketing cookies (Facebook Pixel, Google Ads, etc.)
  if (preferences.marketing) {
    enableMarketing();
  } else {
    disableMarketing();
  }

  // Functional cookies (Chat widgets, etc.)
  if (preferences.functional) {
    enableFunctional();
  } else {
    disableFunctional();
  }
}

/**
 * Enable analytics tracking
 */
function enableAnalytics(): void {
  // Example: Initialize Google Analytics
  // gtag('config', 'GA_MEASUREMENT_ID');
  // console.log("Analytics cookies enabled");
}

/**
 * Disable analytics tracking
 */
function disableAnalytics(): void {
  // Example: Disable Google Analytics
  // gtag('config', 'GA_MEASUREMENT_ID', { send_page_view: false });
  // console.log("Analytics cookies disabled");
}

/**
 * Enable marketing tracking
 */
function enableMarketing(): void {
  // Example: Initialize Facebook Pixel, Google Ads
  // console.log("Marketing cookies enabled");
}

/**
 * Disable marketing tracking
 */
function disableMarketing(): void {
  // Example: Disable marketing pixels
  // console.log("Marketing cookies disabled");
}

/**
 * Enable functional cookies
 */
function enableFunctional(): void {
  // Example: Enable chat widgets, preference storage
  // console.log("Functional cookies enabled");
}

/**
 * Disable functional cookies
 */
function disableFunctional(): void {
  // Example: Disable non-essential functional features
  // console.log("Functional cookies disabled");
}

/**
 * Clear all non-necessary cookies
 */
function clearNonNecessaryCookies(): void {
  // Get all cookies
  const cookies = document.cookie.split(";");

  // List of necessary cookies that should not be deleted
  const necessaryCookies = [CONSENT_STORAGE_KEY, "session", "csrf", "auth"];

  cookies.forEach((cookie) => {
    const [name] = cookie.split("=");
    const cookieName = name.trim();

    // Don't delete necessary cookies
    if (!necessaryCookies.some((necessary) => cookieName.includes(necessary))) {
      // Delete the cookie
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
    }
  });
}

/**
 * Get cookie categories with descriptions
 */
export function getCookieCategories() {
  return {
    necessary: {
      name: "Notwendige Cookies",
      description:
        "Diese Cookies sind für das Funktionieren der Website erforderlich und können nicht deaktiviert werden.",
      required: true,
    },
    analytics: {
      name: "Analyse-Cookies",
      description:
        "Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren.",
      required: false,
    },
    marketing: {
      name: "Marketing-Cookies",
      description:
        "Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen.",
      required: false,
    },
    functional: {
      name: "Funktionale Cookies",
      description:
        "Diese Cookies ermöglichen erweiterte Funktionalitäten und Personalisierung.",
      required: false,
    },
  };
}
