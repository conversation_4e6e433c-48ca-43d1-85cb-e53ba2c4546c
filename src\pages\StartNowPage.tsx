import React from "react";
import { motion } from "framer-motion";
import Hero from "@/components/startNow/hero";
import WhatisConalys from "@/components/startNow/whatisConalys";
import WarumConalys from "@/components/startNow/WarumConalys";
import ThreeSteps from "@/components/startNow/ThreeSteps";
import CTASec from "@/components/startNow/CTASec";
import WhoisConalys from "@/components/startNow/WhoisConalys";
import FooterContact from "@/components/startNow/FooterContact";

const StartNowPage = () => {
  return (
    <>
      {/* HERO SECTION */}
      <Hero />

      {/* SECTION: Was ist Conalys */}
      <WhatisConalys />

      {/* SECTION: Warum Conalys */}
      <WarumConalys />
      {/* <section className="py-20 bg-gray-950 text-gray-200">
        <div className="container mx-auto px-6">
          <h2 className="text-4xl font-bold mb-4">Warum Conalys?</h2>
          <ul className="text-lg text-gray-400 space-y-4">
            <li>
              🔄 Text-zu-Tabelle & zurück: Verträge per Klick in
              Tabellenstruktur verwandeln – und am Ende wieder perfekt in Word
              exportieren.
            </li>
            <li>
              💬 Zentralisierte Kommentare: Strukturierte Spalten für Kommentare
              von „Party A“ & „Party B“.
            </li>
            <li>
              ✅ Status-Anzeige für jede Klausel: Sofort erkennen, welche Punkte
              offen, kritisch oder erledigt sind.
            </li>
            <li>
              💼 Einfach in Ihre Prozesse integrierbar: Kein Tool-Wechsel. Kein
              neues System lernen.
            </li>
          </ul>
        </div>
      </section> */}

      {/* 4. SECTION: Für wen ist Conalys gedacht? */}
      <WhoisConalys />

      {/* 5. SECTION: So funktioniert es – In 3 Schritten */}
      <ThreeSteps />

      {/* 7. SECTION: CTA (Call-to-Action)
       */}
      <CTASec />

      <FooterContact />

      {/* SECTION: Für wen ist Conalys gedacht? */}
      {/* <section className="py-20 bg-gray-900 text-gray-200">
        <div className="container mx-auto px-6">
          <h2 className="text-4xl font-bold mb-4">
            Für wen ist Conalys gedacht?
          </h2>
          <ul className="text-lg text-gray-400 space-y-4">
            <li>
              🎓 Inhouse Legal Teams: Nie wieder E-Mail-Chaos oder
              widersprüchliche Versionen.
            </li>
            <li>
              ⚖️ Anwaltskanzleien: Professionelles Vertragsfeedback für
              Mandanten – klar dokumentiert.
            </li>
            <li>
              🏢 Unternehmen & Einkauf: Mehr Klarheit bei komplexen
              Vertragsverhandlungen.
            </li>
          </ul>
        </div>
      </section> */}
    </>
  );
};

export default StartNowPage;
