import { useEffect, useRef } from "react";

export const useMousePosition = () => {
  const mousePosition = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const updateMousePosition = (ev: MouseEvent) => {
      mousePosition.current = { x: ev.clientX, y: ev.clientY };
    };

    window.addEventListener("mousemove", updateMousePosition);

    return () => {
      window.removeEventListener("mousemove", updateMousePosition);
    };
  }, []);

  return mousePosition;
};

export const useParallax = (depth: number = 20) => {
  const ref = useRef<HTMLDivElement>(null);
  const mousePosition = useMousePosition();

  useEffect(() => {
    const handleMouseMove = () => {
      if (!ref.current) return;

      const { x, y } = mousePosition.current;
      const rect = ref.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      let deltaX = (x - centerX) / depth;
      let deltaY = (y - centerY) / depth;
      // Cap the movement
      deltaX = Math.max(Math.min(deltaX, 20), -20);
      deltaY = Math.max(Math.min(deltaY, 20), -20);

      ref.current.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    };

    window.addEventListener("mousemove", handleMouseMove);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [depth]);

  return ref;
};
