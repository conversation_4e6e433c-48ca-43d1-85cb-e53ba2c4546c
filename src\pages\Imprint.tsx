import { motion } from "framer-motion";

export default function ImprintPage() {
  return (
    <section className="py-32 bg-gray-950 text-gray-200">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />

      {/* Animated Gradient Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 blur-3xl animate-drift" />
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-cyan-500/30 to-blue-500/30 blur-3xl animate-drift-slow" />
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,black_40%,transparent_100%)]" />
      {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" /> */}
      <div className="container max-w-3xl mx-auto ">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div>
            {/* <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-8"> */}
            <h1 className="text-3xl font-bold mb-8">Impressum</h1>
            <div className="space-y-8">
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Angaben gemäß § 5 TMG
                </h2>
                <p>Gainback GmbH</p>
                <p>Falkenstein 16a</p>
                <p>97499 Donnersdorf</p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Registereintrag</h2>
                <p>Registergericht: Amtsgericht Schweinfurt</p>
                <p>Handelsregisternummer: HRB 9314</p>
                <p>Umsatzsteuer-ID: DE365388597</p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Vertreten durch</h2>
                <p>Jakob Hebenstreit und Lorenz Wolf</p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Kontakt</h2>
                <p>E-Mail: <EMAIL></p>
                <p>Telefon: +49 176 23159003</p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Streitschlichtung
                </h2>
                <p className="text-muted-foreground">
                  Die EU-Kommission hat eine Internetplattform zur
                  Online-Beilegung von Streitigkeiten (OS-Plattform) zwischen
                  Unternehmern und Verbrauchern eingerichtet. Die OS-Plattform
                  ist erreichbar unter{" "}
                  <a
                    href="https://ec.europa.eu/consumers/odr/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline"
                  >
                    https://ec.europa.eu/consumers/odr/
                  </a>
                </p>
                <p className="mt-2 text-muted-foreground">
                  Wir sind nicht bereit und nicht verpflichtet, an einem
                  Streitbeilegungsverfahren vor einer
                  Verbraucherschlichtungsstelle teilzunehmen.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Haftung für Inhalte
                </h2>
                <p className="text-muted-foreground">
                  Wir entwickeln die Inhalte dieser Website ständig weiter und
                  bemühen uns korrekte und aktuelle Informationen
                  bereitzustellen. Leider können wir keine Haftung für die
                  Korrektheit aller Inhalte auf dieser Website übernehmen,
                  speziell für jene, die seitens Dritter bereitgestellt wurden.
                </p>
                <p className="mt-2 text-muted-foreground">
                  Als Diensteanbieter sind wir nicht verpflichtet, die von Ihnen
                  übermittelten oder gespeicherten Informationen zu überwachen
                  oder nach Umständen zu forschen, die auf eine rechtswidrige
                  Tätigkeit hinweisen.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Haftung für Links
                </h2>
                <p className="text-muted-foreground">
                  Unsere Website enthält Links zu anderen Websites für deren
                  Inhalt wir nicht verantwortlich sind. Haftung für verlinkte
                  Websites besteht für uns nicht, da wir keine Kenntnis
                  rechtswidriger Tätigkeiten hatten und haben.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Urheberrecht</h2>
                <p className="text-muted-foreground">
                  Alle Inhalte dieser Webseite (Bilder, Fotos, Texte, Videos)
                  unterliegen dem Urheberrecht. Bitte fragen Sie uns bevor Sie
                  die Inhalte dieser Website verbreiten, vervielfältigen oder
                  verwerten. Falls notwendig, werden wir die unerlaubte Nutzung
                  von Teilen der Inhalte unserer Seite rechtlich verfolgen.
                </p>
              </section>
            </div>
            {/* </div> */}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
