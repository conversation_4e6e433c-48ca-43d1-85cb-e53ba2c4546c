<!DOCTYPE html>
<html>
<head>
    <title>Test Google Apps Script</title>
</head>
<body>
    <h1>Test Google Apps Script Connection</h1>
    <button onclick="testScript()">Test Script</button>
    <div id="result"></div>

    <script>
        async function testScript() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            const testData = {
                name: 'Test User',
                email: '<EMAIL>',
                subject: 'Test Subject',
                message: 'This is a test message',
                origin: window.location.origin
            };

            try {
                console.log('Testing Google Apps Script...');
                console.log('URL:', 'https://script.google.com/macros/s/AKfycbwb_KzACvMPt6U0ztypzTtly8TCwDffUnOmXhTbZnmP7jdTJBMpsCo0xAwcBv0nAfh_wg/exec');
                console.log('Data:', testData);

                const response = await fetch('https://script.google.com/macros/s/AKfycbwb_KzACvMPt6U0ztypzTtly8TCwDffUnOmXhTbZnmP7jdTJBMpsCo0xAwcBv0nAfh_wg/exec', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                    mode: 'cors'
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    resultDiv.innerHTML = `<div style="color: red;">Error: ${response.status} - ${errorText}</div>`;
                    return;
                }

                const result = await response.json();
                console.log('Success result:', result);
                
                if (result.success) {
                    resultDiv.innerHTML = '<div style="color: green;">✅ Script is working! Check your email.</div>';
                } else {
                    resultDiv.innerHTML = `<div style="color: orange;">⚠️ Script responded but with error: ${result.error}</div>`;
                }

            } catch (error) {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `<div style="color: red;">❌ Connection failed: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
