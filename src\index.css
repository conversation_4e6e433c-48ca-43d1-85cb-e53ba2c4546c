@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: dark;
}

body {
  @apply bg-gray-950 text-white antialiased;
  font-family: "Inter", system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Animations */
@keyframes drift {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(50px, 50px) rotate(180deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

@keyframes drift-slow {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(-50px, -50px) rotate(-180deg);
  }
  100% {
    transform: translate(0, 0) rotate(-360deg);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-drift {
  animation: drift 20s infinite linear;
}

.animate-drift-slow {
  animation: drift-slow 25s infinite linear;
}

.animate-gradient {
  background-size: 200% auto;
  animation: gradient 8s linear infinite;
}

/* Glass effect utilities */
.glass {
  @apply backdrop-blur-lg bg-opacity-20 border border-gray-700/50;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Text gradient */
.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400;
}

/* Button styles */
.btn-primary {
  @apply relative bg-white text-gray-900 px-6 py-3 rounded-xl font-medium transition-all duration-300;
}

.btn-primary:hover {
  @apply shadow-lg shadow-white/10;
}

/* Card styles */
.card {
  @apply relative bg-gray-800/40 backdrop-blur-xl rounded-3xl border border-gray-700/50 transition-all duration-300;
}

.card:hover {
  @apply border-gray-600/50;
}

/* Grid background */
.grid-bg {
  @apply bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)];
  background-size: 32px 32px;
}

h2 {
  line-height: 1.4 !important;
}

/* Custom Cursor Styles - Keep default cursor visible */
/* Remove the cursor: none to show default cursor */

/* Ensure interactive elements are properly detected */
a,
button,
[role="button"],
input,
textarea,
select,
[tabindex] {
  position: relative;
}

/* Cursor animations */
@keyframes cursor-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes cursor-glow {
  0%,
  100% {
    box-shadow:
      0 0 15px rgba(59, 130, 246, 0.8),
      0 0 30px rgba(147, 51, 234, 0.4);
  }
  50% {
    box-shadow:
      0 0 25px rgba(59, 130, 246, 1),
      0 0 50px rgba(147, 51, 234, 0.8);
  }
}

.cursor-pulse {
  animation: cursor-pulse 2s ease-in-out infinite;
}

.cursor-glow {
  animation: cursor-glow 2s ease-in-out infinite;
}

/* Keep default cursor on all devices */
/* Default cursor remains visible alongside custom cursor animation */

/* Smooth transitions for cursor interactions */
a,
button,
[role="button"] {
  transition: all 0.2s ease-out;
}
