/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        gray: {
          950: '#0a0a0a',
        },
      },
      animation: {
        'fade-in': 'fade-in 0.6s ease-out forwards',
        'drift': 'drift 20s infinite linear',
        'drift-slow': 'drift-slow 25s infinite linear',
        'gradient': 'gradient 8s linear infinite',
        'gradient-xy': 'gradient-xy 15s ease infinite',
        'blink': 'blink 1s step-end infinite',
      },
      keyframes: {
        'fade-in': {
          '0%': {
            opacity: '0',
            transform: 'translateY(20px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'drift': {
          '0%': {
            transform: 'translate(0, 0) rotate(0deg)',
          },
          '50%': {
            transform: 'translate(100px, 100px) rotate(180deg)',
          },
          '100%': {
            transform: 'translate(0, 0) rotate(360deg)',
          },
        },
        'drift-slow': {
          '0%': {
            transform: 'translate(0, 0) rotate(0deg)',
          },
          '50%': {
            transform: 'translate(-100px, -100px) rotate(-180deg)',
          },
          '100%': {
            transform: 'translate(0, 0) rotate(-360deg)',
          },
        },
        'gradient': {
          '0%': {
            'background-position': '0% 50%',
          },
          '50%': {
            'background-position': '100% 50%',
          },
          '100%': {
            'background-position': '0% 50%',
          },
        },
        'gradient-xy': {
          '0%, 100%': {
            'background-position': '0% 0%',
          },
          '50%': {
            'background-position': '100% 100%',
          },
        },
        'blink': {
          '0%, 100%': {
            opacity: '1',
          },
          '50%': {
            opacity: '0',
          },
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}