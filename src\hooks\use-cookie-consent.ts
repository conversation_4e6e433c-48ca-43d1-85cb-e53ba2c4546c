import { useState, useEffect, useCallback } from "react";
import {
  getCookieConsent,
  saveCookieConsent,
  hasUserConsented,
  getCookiePreferences,
  acceptAllCookies,
  acceptNecessaryCookies,
  clearCookieConsent,
  CookiePreferences,
  CookieConsentData,
} from "@/utils/cookies";

export interface UseCookieConsentReturn {
  // State
  hasConsented: boolean;
  showBanner: boolean;
  preferences: CookiePreferences;
  consentData: CookieConsentData | null;

  // Actions
  acceptAll: () => void;
  acceptNecessary: () => void;
  savePreferences: (preferences: CookiePreferences) => void;
  clearConsent: () => void;
  hideBanner: () => void;
  showBannerManually: () => void;

  // Utilities
  isLoading: boolean;
}

// Helper function to check if current page is privacy-related
function isPrivacyRelatedPage(): boolean {
  const currentPath = window.location.pathname;
  return (
    currentPath === "/privacy" ||
    currentPath === "/datenschutz" ||
    currentPath === "/terms" ||
    currentPath === "/imprint"
  );
}

export function useCookieConsent(): UseCookieConsentReturn {
  const [hasConsented, setHasConsented] = useState(false);
  const [showBannerState, setShowBannerState] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  });
  const [consentData, setConsentData] = useState<CookieConsentData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  // Initialize consent state
  useEffect(() => {
    const initializeConsent = () => {
      try {
        const consent = getCookieConsent();
        const userHasConsented = hasUserConsented();
        const currentPreferences = getCookiePreferences();

        setConsentData(consent);
        setHasConsented(userHasConsented);
        setPreferences(currentPreferences);

        // Check if we're on privacy-related pages where popup shouldn't show
        const isPrivacyPage = isPrivacyRelatedPage();
        console.log(
          isPrivacyPage,
          "isPrivacyPage",
          userHasConsented,
          !userHasConsented && !isPrivacyPage
        );
        // Only show popup if user hasn't consented yet AND not on privacy pages
        setShowBannerState(!userHasConsented && !isPrivacyPage);
        setIsLoading(false);
      } catch (error) {
        console.error("Error initializing cookie consent:", error);
        setIsLoading(false);
      }
    };

    initializeConsent();
  }, []);

  // Accept all cookies
  const acceptAll = useCallback(() => {
    // Immediately hide the banner
    setShowBannerState(false);
    setHasConsented(true);

    // Save consent in background
    setTimeout(() => {
      try {
        acceptAllCookies();
        const newConsent = getCookieConsent();
        const newPreferences = getCookiePreferences();

        setConsentData(newConsent);
        setPreferences(newPreferences);

        // Trigger custom event for other components
        window.dispatchEvent(
          new CustomEvent("cookieConsentChanged", {
            detail: { hasConsented: true, preferences: newPreferences },
          })
        );
      } catch (error) {
        console.error("Error accepting all cookies:", error);
      }
    }, 0);
  }, []);

  // Accept only necessary cookies
  const acceptNecessary = useCallback(() => {
    // Immediately hide the banner
    setShowBannerState(false);
    setHasConsented(true);

    // Save consent in background
    setTimeout(() => {
      try {
        acceptNecessaryCookies();
        const newConsent = getCookieConsent();
        const newPreferences = getCookiePreferences();

        setConsentData(newConsent);
        setPreferences(newPreferences);

        // Trigger custom event for other components
        window.dispatchEvent(
          new CustomEvent("cookieConsentChanged", {
            detail: { hasConsented: true, preferences: newPreferences },
          })
        );
      } catch (error) {
        console.error("Error accepting necessary cookies:", error);
      }
    }, 0);
  }, []);

  // Save custom preferences
  const savePreferences = useCallback((newPreferences: CookiePreferences) => {
    try {
      saveCookieConsent(newPreferences);
      const newConsent = getCookieConsent();

      setConsentData(newConsent);
      setHasConsented(true);
      setPreferences(newPreferences);
      setShowBannerState(false);

      // Trigger custom event for other components
      window.dispatchEvent(
        new CustomEvent("cookieConsentChanged", {
          detail: { hasConsented: true, preferences: newPreferences },
        })
      );
    } catch (error) {
      console.error("Error saving cookie preferences:", error);
    }
  }, []);

  // Clear consent (for testing or user request)
  const clearConsent = useCallback(() => {
    try {
      clearCookieConsent();

      setConsentData(null);
      setHasConsented(false);
      setPreferences({
        necessary: true,
        analytics: false,
        marketing: false,
        functional: false,
      });
      setShowBannerState(true);

      // Trigger custom event for other components
      window.dispatchEvent(
        new CustomEvent("cookieConsentChanged", {
          detail: { hasConsented: false, preferences: null },
        })
      );
    } catch (error) {
      console.error("Error clearing cookie consent:", error);
    }
  }, []);

  // Hide banner manually
  const hideBanner = useCallback(() => {
    setShowBannerState(false);
  }, []);

  // Show banner manually
  const showBannerManually = useCallback(() => {
    setShowBannerState(true);
  }, []);

  return {
    // State
    hasConsented,
    showBanner: showBannerState,
    preferences,
    consentData,

    // Actions
    acceptAll,
    acceptNecessary,
    savePreferences,
    clearConsent,
    hideBanner,
    showBannerManually,

    // Utilities
    isLoading,
  };
}
