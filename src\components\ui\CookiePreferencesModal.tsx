import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCookieConsent } from '@/hooks/use-cookie-consent';
import { getCookieCategories, CookiePreferences } from '@/utils/cookies';

interface CookiePreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CookiePreferencesModal({ isOpen, onClose }: CookiePreferencesModalProps) {
  const { preferences, savePreferences, acceptAll, acceptNecessary } = useCookieConsent();
  const [localPreferences, setLocalPreferences] = useState<CookiePreferences>(preferences);
  const cookieCategories = getCookieCategories();

  // Update local preferences when modal opens
  useEffect(() => {
    if (isOpen) {
      setLocalPreferences(preferences);
    }
  }, [isOpen, preferences]);

  const handleToggle = (category: keyof CookiePreferences) => {
    if (category === 'necessary') return; // Cannot toggle necessary cookies
    
    setLocalPreferences(prev => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const handleSave = () => {
    savePreferences(localPreferences);
    onClose();
  };

  const handleAcceptAll = () => {
    acceptAll();
    onClose();
  };

  const handleAcceptNecessary = () => {
    acceptNecessary();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[10000] flex items-center justify-center p-4"
        onClick={onClose}
      >
        {/* Backdrop */}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
        
        {/* Modal */}
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl" />
            
            <div className="relative">
              {/* Header */}
              <div className="p-6 border-b border-gray-700/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <span className="text-xl">🍪</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-white">Cookie-Einstellungen</h2>
                      <p className="text-gray-400 text-sm">Verwalten Sie Ihre Cookie-Präferenzen</p>
                    </div>
                  </div>
                  
                  <motion.button
                    onClick={onClose}
                    className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white rounded-lg hover:bg-gray-700/50 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </motion.button>
                </div>
              </div>
              
              {/* Content */}
              <div className="p-6 max-h-[60vh] overflow-y-auto">
                <div className="space-y-6">
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Wir verwenden verschiedene Arten von Cookies, um Ihnen die beste Erfahrung zu bieten. 
                    Sie können wählen, welche Kategorien Sie zulassen möchten.
                  </p>
                  
                  {/* Cookie Categories */}
                  <div className="space-y-4">
                    {Object.entries(cookieCategories).map(([key, category]) => {
                      const isEnabled = localPreferences[key as keyof CookiePreferences];
                      const isRequired = category.required;
                      
                      return (
                        <motion.div
                          key={key}
                          className="bg-gray-800/40 rounded-xl p-4 border border-gray-700/30"
                          whileHover={{ borderColor: 'rgba(99, 102, 241, 0.3)' }}
                        >
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1">
                              <h3 className="font-medium text-white mb-1">{category.name}</h3>
                              <p className="text-gray-400 text-sm leading-relaxed">{category.description}</p>
                              {isRequired && (
                                <span className="inline-block mt-2 px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-md">
                                  Erforderlich
                                </span>
                              )}
                            </div>
                            
                            {/* Toggle Switch */}
                            <motion.button
                              onClick={() => handleToggle(key as keyof CookiePreferences)}
                              disabled={isRequired}
                              className={`relative w-12 h-6 rounded-full transition-colors ${
                                isEnabled 
                                  ? 'bg-gradient-to-r from-blue-500 to-purple-500' 
                                  : 'bg-gray-600'
                              } ${isRequired ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                              whileTap={{ scale: isRequired ? 1 : 0.95 }}
                            >
                              <motion.div
                                className="absolute top-0.5 w-5 h-5 bg-white rounded-full shadow-lg"
                                animate={{ x: isEnabled ? 26 : 2 }}
                                transition={{ duration: 0.2, ease: "easeOut" }}
                              />
                            </motion.button>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              </div>
              
              {/* Footer */}
              <div className="p-6 border-t border-gray-700/50">
                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <motion.button
                    onClick={handleAcceptNecessary}
                    className="px-6 py-3 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 rounded-xl transition-all duration-200 text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Nur Notwendige
                  </motion.button>
                  
                  <motion.button
                    onClick={handleAcceptAll}
                    className="px-6 py-3 text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600 rounded-xl transition-all duration-200 text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Alle akzeptieren
                  </motion.button>
                  
                  <motion.button
                    onClick={handleSave}
                    className="px-6 py-3 text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-xl transition-all duration-200 text-sm font-medium shadow-lg shadow-blue-500/25"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Einstellungen speichern
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
