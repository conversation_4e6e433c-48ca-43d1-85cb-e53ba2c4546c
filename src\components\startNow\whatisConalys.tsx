import { useParallax } from "@/hooks/use-parallax";
import { motion, useScroll, useTransform } from "framer-motion";
import { useInView } from "react-intersection-observer";

const WhatisConalys = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, 0]);
  const parallaxRef = useParallax(25);
  return (
    <section id="whatisconalys" className="relative py-24 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:24px_24px]" />
      <motion.div
        ref={ref}
        style={{ y }}
        className="container mx-auto px-6 relative z-10"
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <motion.h2
            ref={parallaxRef}
            className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 "
          >
            Was ist Conalys?
          </motion.h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Conalys ist ein intelligentes Microsoft Word Add-In für
            kollaboratives Vertragsmanagement. Statt mühsamer Word-Kommentare
            oder manueller Excel-Tabellen bietet Conalys ein strukturiertes
            Review-System direkt in Word – mit Umwandlungsfunktionen,
            Änderungsverfolgung und klaren Statusanzeigen.
          </p>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default WhatisConalys;
