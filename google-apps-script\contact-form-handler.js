/**
 * Google Apps Script for handling contact form submissions
 * This script will send emails to both admin and user automatically
 */

// Configuration - UPDATE THESE VALUES
const CONFIG = {
  ADMIN_EMAIL: '<EMAIL>', // Replace with your admin email
  ADMIN_NAME: 'Conalys Support Team',
  COMPANY_NAME: 'Conalys',
  COMPANY_WEBSITE: 'https://conalys.com',

  // Email templates
  ADMIN_SUBJECT: 'Neue Kontaktanfrage von {name}',
  USER_SUBJECT: 'Vielen Dank für Ihre Nachricht - {company}',

  // CORS settings
  ALLOWED_ORIGINS: [
    'http://localhost:5173',
    'http://localhost:3000',
    'https://conalys.com',
    'https://www.conalys.com'
  ]
};

function doPost(e) {
  try {
    let data = e.postData.type === 'application/json'
      ? JSON.parse(e.postData.contents)
      : e.parameter;

    const { name, email, subject, message, origin } = data;

    if (!name || !email || !subject || !message) {
      throw new Error("All fields are required.");
    }

    if (!isValidEmail(email)) {
      throw new Error("Invalid email.");
    }

    // Optional: Check allowed origins
    if (origin && !CONFIG.ALLOWED_ORIGINS.includes(origin)) {
      throw new Error("Origin not allowed.");
    }

    const adminSent = sendAdminEmail(name, email, subject, message);
    const userSent = sendUserEmail(name, email, subject);

    const response = {
      success: true,
      message: "Emails sent successfully"
    };

    return ContentService
      .createTextOutput(JSON.stringify(response))
      .setMimeType(ContentService.MimeType.JSON);  // :white_check_mark: JSON returned
  } catch (err) {
    const errorResponse = {
      success: false,
      error: err.message
    };
    return ContentService
      .createTextOutput(JSON.stringify(errorResponse))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function buildResponse(statusCode, data, origin = '*') {
  const json = JSON.stringify(data);

  const output = HtmlService.createHtmlOutput(json);

  // Even though we want to return JSON, we cannot set MimeType.JSON here.
  // Instead, we set proper headers so the client treats it as JSON.

  output.setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL); // optional
  output.setContent(json);

  // ✅ Set CORS headers manually
  output.setHeader("Content-Type", "application/json");
  output.setHeader("Access-Control-Allow-Origin", origin);
  output.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
  output.setHeader("Access-Control-Allow-Headers", "Content-Type");

  return output;
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
function doOptions() {
  return HtmlService.createHtmlOutput('')
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    });
}


/**
 * Send email to admin with contact form details
 */
function sendAdminEmail(name, email, subject, message) {
  try {
    const emailSubject = CONFIG.ADMIN_SUBJECT.replace('{name}', name);

    const htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Neue Kontaktanfrage</title>
      </head>
      <body style="margin: 0; padding: 0; background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #**********%); font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background: #0f172a;">

          <!-- Header with gradient -->
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #3b82f6 100%); padding: 40px 30px; text-align: center; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.3;"></div>
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.3); position: relative; z-index: 1;">
              🚀 Neue Kontaktanfrage
            </h1>
            <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px; position: relative; z-index: 1;">
              ${CONFIG.COMPANY_NAME} Website
            </p>
          </div>

          <!-- Main content -->
          <div style="padding: 40px 30px; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);">

            <!-- Contact details card -->
            <div style="background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 16px; padding: 30px; margin-bottom: 30px; backdrop-filter: blur(10px);">
              <h2 style="color: #f1f5f9; margin: 0 0 25px 0; font-size: 20px; font-weight: 600; display: flex; align-items: center;">
                <span style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); width: 8px; height: 8px; border-radius: 50%; display: inline-block; margin-right: 12px;"></span>
                Kontaktdetails
              </h2>

              <div style="space-y: 15px;">
                <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid rgba(59, 130, 246, 0.2);">
                  <div style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="color: white; font-size: 18px;">👤</span>
                  </div>
                  <div>
                    <p style="color: #94a3b8; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">Name</p>
                    <p style="color: #f1f5f9; margin: 5px 0 0 0; font-size: 16px; font-weight: 500;">${name}</p>
                  </div>
                </div>

                <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid rgba(59, 130, 246, 0.2);">
                  <div style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="color: white; font-size: 18px;">📧</span>
                  </div>
                  <div>
                    <p style="color: #94a3b8; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">E-Mail</p>
                    <p style="color: #f1f5f9; margin: 5px 0 0 0; font-size: 16px; font-weight: 500;">
                      <a href="mailto:${email}" style="color: #60a5fa; text-decoration: none;">${email}</a>
                    </p>
                  </div>
                </div>

                <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid rgba(59, 130, 246, 0.2);">
                  <div style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="color: white; font-size: 18px;">💬</span>
                  </div>
                  <div>
                    <p style="color: #94a3b8; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">Betreff</p>
                    <p style="color: #f1f5f9; margin: 5px 0 0 0; font-size: 16px; font-weight: 500;">${subject}</p>
                  </div>
                </div>

              </div>
            </div>

            <!-- Message card -->
            <div style="background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 16px; padding: 30px; margin-bottom: 30px; backdrop-filter: blur(10px);">
              <h3 style="color: #f1f5f9; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; display: flex; align-items: center;">
                <span style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); width: 8px; height: 8px; border-radius: 50%; display: inline-block; margin-right: 12px;"></span>
                Nachricht
              </h3>
              <div style="background: rgba(15, 23, 42, 0.8); padding: 25px; border-radius: 12px; border-left: 4px solid #3b82f6;">
                <p style="color: #e2e8f0; margin: 0; line-height: 1.6; font-size: 15px; white-space: pre-wrap;">${message}</p>
              </div>
            </div>

            <!-- Action button -->
            <div style="text-align: center; margin-top: 40px;">
              <a href="mailto:${email}" style="display: inline-block; background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 15px 30px; border-radius: 12px; text-decoration: none; font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);">
                📧 Antworten
              </a>
            </div>
          </div>

          <!-- Footer -->
          <div style="background: #0f172a; padding: 30px; text-align: center; border-top: 1px solid rgba(59, 130, 246, 0.2);">
            <p style="color: #64748b; margin: 0; font-size: 14px;">
              © ${new Date().getFullYear()} ${CONFIG.COMPANY_NAME} • Automatisch generiert
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    MailApp.sendEmail({
      to: CONFIG.ADMIN_EMAIL,
      subject: emailSubject,
      htmlBody: htmlBody,
      replyTo: email
    });

    return true;
  } catch (error) {
    console.error('Error sending admin email:', error);
    return false;
  }
}

/**
 * Send confirmation email to user
 */
function sendUserEmail(name, email, subject) {
  try {
    const emailSubject = CONFIG.USER_SUBJECT.replace('{company}', CONFIG.COMPANY_NAME);

    const htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vielen Dank für Ihre Nachricht</title>
      </head>
      <body style="margin: 0; padding: 0; background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #**********%); font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; background: #0f172a;">

          <!-- Header with company branding -->
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #3b82f6 100%); padding: 50px 30px; text-align: center; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.3;"></div>

            <!-- Company logo placeholder -->
            <div style="background: rgba(255,255,255,0.2); width: 80px; height: 80px; border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; position: relative; z-index: 1;">
              <span style="font-size: 36px; color: white;">🚀</span>
            </div>

            <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.3); position: relative; z-index: 1;">
              ${CONFIG.COMPANY_NAME}
            </h1>
            <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 18px; position: relative; z-index: 1;">
              Vielen Dank für Ihre Nachricht!
            </p>
          </div>

          <!-- Main content -->
          <div style="padding: 50px 30px; background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);">

            <!-- Welcome message -->
            <div style="text-align: center; margin-bottom: 40px;">
              <div style="background: rgba(34, 197, 94, 0.2); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(34, 197, 94, 0.3);">
                <span style="font-size: 36px;">✅</span>
              </div>
              <h2 style="color: #f1f5f9; margin: 0 0 15px 0; font-size: 28px; font-weight: 700;">
                Hallo ${name}!
              </h2>
              <p style="color: #94a3b8; margin: 0; font-size: 18px; line-height: 1.6;">
                Ihre Nachricht wurde erfolgreich übermittelt
              </p>
            </div>

            <!-- Thank you message -->
            <div style="background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 20px; padding: 40px; margin-bottom: 30px; backdrop-filter: blur(10px); text-align: center;">
              <h3 style="color: #f1f5f9; margin: 0 0 20px 0; font-size: 22px; font-weight: 600;">
                🎉 Vielen Dank für Ihr Interesse!
              </h3>
              <p style="color: #e2e8f0; margin: 0 0 25px 0; font-size: 16px; line-height: 1.7;">
                Wir haben Ihre Anfrage erhalten und freuen uns über Ihr Interesse an ${CONFIG.COMPANY_NAME}.
                Unser Team wird Ihre Nachricht sorgfältig prüfen und sich innerhalb von <strong style="color: #60a5fa;">24 Stunden</strong> bei Ihnen melden.
              </p>

              <!-- Submission details -->
              <div style="background: rgba(15, 23, 42, 0.8); border-radius: 16px; padding: 25px; margin: 25px 0; border-left: 4px solid #3b82f6;">
                <h4 style="color: #f1f5f9; margin: 0 0 15px 0; font-size: 16px; font-weight: 600; display: flex; align-items: center; justify-content: center;">
                  <span style="margin-right: 8px;">📋</span> Ihre Anfrage im Überblick
                </h4>
                <div style="text-align: left;">
                  <p style="color: #94a3b8; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #f1f5f9;">Betreff:</strong> ${subject}
                  </p>
                  <p style="color: #94a3b8; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #f1f5f9;">Gesendet am:</strong> ${new Date().toLocaleString('de-DE')}
                  </p>
                  <p style="color: #94a3b8; margin: 8px 0; font-size: 14px;">
                    <strong style="color: #f1f5f9;">Referenz-ID:</strong> #${Date.now().toString().slice(-6)}
                  </p>
                </div>
              </div>
            </div>

            <!-- Contact info -->
           <!-- <div style="background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 20px; padding: 30px; margin-bottom: 30px; backdrop-filter: blur(10px); text-align: center;">
              <h3 style="color: #f1f5f9; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                💬 Haben Sie dringende Fragen?
              </h3>
              <p style="color: #94a3b8; margin: 0 0 20px 0; font-size: 15px; line-height: 1.6;">
                Falls Sie nicht warten möchten, können Sie uns auch direkt kontaktieren:
              </p>
              <a href="mailto:${CONFIG.ADMIN_EMAIL}" style="display: inline-block; background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 12px 25px; border-radius: 10px; text-decoration: none; font-weight: 600; font-size: 14px; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);">
                📧 ${CONFIG.ADMIN_EMAIL}
              </a>
            </div>

            <!-- Website link -->
            <div style="text-align: center; margin-top: 40px;">
              <a href="${CONFIG.COMPANY_WEBSITE}" style="display: inline-block; background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); color: #60a5fa; padding: 15px 30px; border-radius: 12px; text-decoration: none; font-weight: 600; font-size: 16px;">
                🌐 Website besuchen
              </a>
            </div>
          </div> -->

          <!-- Footer -->
          <div style="background: #0f172a; padding: 40px 30px; text-align: center; border-top: 1px solid rgba(59, 130, 246, 0.2);">
            <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
              © ${new Date().getFullYear()} ${CONFIG.COMPANY_NAME}. Alle Rechte vorbehalten.
            </p>
            <p style="color: #475569; margin: 0; font-size: 12px;">
              Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht direkt auf diese E-Mail.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    MailApp.sendEmail({
      to: email,
      subject: emailSubject,
      htmlBody: htmlBody,
      replyTo: CONFIG.ADMIN_EMAIL
    });

    return true;
  } catch (error) {
    console.error('Error sending user email:', error);
    return false;
  }
}

/**
 * Test function to verify email sending works
 * Run this manually in Google Apps Script to test
 */
function testEmailSending() {
  console.log('Testing email sending...');
  console.log('Admin email configured as:', CONFIG.ADMIN_EMAIL);

  try {
    // Test admin email
    const adminResult = sendAdminEmail(
      'Test User',
      '<EMAIL>',
      'Test Subject',
      'This is a test message from Google Apps Script'
    );
    console.log('Admin email test result:', adminResult);

    // Test user email
    const userResult = sendUserEmail(
      'Test User',
      CONFIG.ADMIN_EMAIL, // Send to admin email for testing
      'Test Subject'
    );
    console.log('User email test result:', userResult);

    if (adminResult && userResult) {
      console.log('✅ Email sending test PASSED');
      return 'SUCCESS: Both emails sent successfully';
    } else {
      console.log('❌ Email sending test FAILED');
      return 'FAILED: One or both emails failed to send';
    }
  } catch (error) {
    console.error('Email test error:', error);
    return 'ERROR: ' + error.toString();
  }
}

/**
 * Test function for doPost
 */
function testDoPost() {
  // Create a mock POST request
  const e = {
    postData: {
      type: 'application/json',
      contents: JSON.stringify({
        name: "Test User",
        email: "<EMAIL>",
        subject: "Test Subject",
        message: "This is a test message",
        origin: "https://conalys.com"
      })
    }
  };

  // Call doPost with the mock data
  const result = doPost(e);
  Logger.log(result.getContent());
}

/**
 * Validate email format
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Create HTTP response with CORS headers
 */
function createResponse(statusCode, data) {
  const response = ContentService.createTextOutput(JSON.stringify(data));
  response.setMimeType(ContentService.MimeType.JSON);

  // In Google Apps Script, we can't set headers directly on the response object
  // Instead, we return an object with both content and headers
  return {
    response: response,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  };
}