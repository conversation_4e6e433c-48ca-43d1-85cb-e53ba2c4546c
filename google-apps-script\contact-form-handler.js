/**
 * Google Apps Script for handling contact form submissions
 * This script will send emails to both admin and user automatically
 */

// Configuration - UPDATE THESE VALUES
const CONFIG = {
  ADMIN_EMAIL: '<EMAIL>', // Replace with your admin email
  ADMIN_NAME: 'Conalys Support Team',
  COMPANY_NAME: 'Conalys',
  COMPANY_WEBSITE: 'https://conalys.com',

  // Email templates
  ADMIN_SUBJECT: 'Neue Kontaktanfrage von {name}',
  USER_SUBJECT: 'Vielen Dank für Ihre Nachricht - {company}',

  // CORS settings
  ALLOWED_ORIGINS: [
    'http://localhost:5173',
    'http://localhost:3000',
    'https://conalys.com',
    'https://www.conalys.com'
  ]
};

function doPost(e) {
  try {
    let data = e.postData.type === 'application/json'
      ? JSON.parse(e.postData.contents)
      : e.parameter;

    const { name, email, subject, message, origin } = data;

    if (!name || !email || !subject || !message) {
      throw new Error("All fields are required.");
    }

    if (!isValidEmail(email)) {
      throw new Error("Invalid email.");
    }

    // Optional: Check allowed origins
    if (origin && !CONFIG.ALLOWED_ORIGINS.includes(origin)) {
      throw new Error("Origin not allowed.");
    }

    const adminSent = sendAdminEmail(name, email, subject, message);
    const userSent = sendUserEmail(name, email, subject);

    const response = {
      success: true,
      message: "Emails sent successfully"
    };

    return ContentService
      .createTextOutput(JSON.stringify(response))
      .setMimeType(ContentService.MimeType.JSON);  // :white_check_mark: JSON returned
  } catch (err) {
    const errorResponse = {
      success: false,
      error: err.message
    };
    return ContentService
      .createTextOutput(JSON.stringify(errorResponse))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function buildResponse(statusCode, data, origin = '*') {
  const json = JSON.stringify(data);

  const output = HtmlService.createHtmlOutput(json);

  // Even though we want to return JSON, we cannot set MimeType.JSON here.
  // Instead, we set proper headers so the client treats it as JSON.

  output.setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL); // optional
  output.setContent(json);

  // ✅ Set CORS headers manually
  output.setHeader("Content-Type", "application/json");
  output.setHeader("Access-Control-Allow-Origin", origin);
  output.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
  output.setHeader("Access-Control-Allow-Headers", "Content-Type");

  return output;
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
function doOptions() {
  return HtmlService.createHtmlOutput('')
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    });
}


/**
 * Send email to admin with contact form details
 */
function sendAdminEmail(name, email, subject, message) {
  try {
    const emailSubject = CONFIG.ADMIN_SUBJECT.replace('{name}', name);

    const htmlBody = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>Neue Kontaktanfrage</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc;">
          <tr>
            <td align="center" style="padding: 20px 0;">
              <table border="0" cellpadding="0" cellspacing="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

                <!-- Header -->
                <tr>
                  <td style="background-color: #3b82f6; padding: 40px 30px; text-align: center; border-radius: 8px 8px 0 0;">
                    <!-- Company Logo -->
                    <table border="0" cellpadding="0" cellspacing="0" align="center" style="margin-bottom: 20px;">
                      <tr>
                        <td>
                          <img src="https://firebasestorage.googleapis.com/v0/b/gainback-9023c.appspot.com/o/test%2FCONALYS%20-%20White.png?alt=media&token=e341d001-3d37-4b75-b211-79cb8038b14b" alt="${CONFIG.COMPANY_NAME} Logo" style="height: 60px; width: auto; display: block;" />
                        </td>
                      </tr>
                    </table>

                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">
                      Neue Kontaktanfrage
                    </h1>
                   <!-- <p style="color: #e0f2fe; margin: 10px 0 0 0; font-size: 16px;">  -->
                    <!--  ${CONFIG.COMPANY_NAME} Website  -->
                    <!-- </p>  -->
                  </td>
                </tr>

                <!-- Main Content -->
                <tr>
                  <td style="padding: 40px 30px; background-color: #ffffff;">

                    <!-- Contact Details Section -->
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 30px;">
                      <tr>
                        <td style="padding: 30px;">
                          <h2 style="color: #1e293b; margin: 0 0 25px 0; font-size: 20px; font-weight: bold;">
                            📋 Kontaktangaben
                          </h2>

                          <!-- Name Row -->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 20px;">
                            <tr>
                              <td width="50" style="vertical-align: top; padding-right: 15px;">
                                <div style="background-color: #3b82f6; width: 40px; height: 40px; border-radius: 8px; text-align: center; line-height: 40px;">
                                  <span style="color: white; font-size: 18px;">👤</span>
                                </div>
                              </td>
                              <td style="vertical-align: top; border-bottom: 1px solid #e2e8f0; padding-bottom: 15px;">
                                <p style="color: #64748b; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">NAME</p>
                                <p style="color: #1e293b; margin: 5px 0 0 0; font-size: 16px; font-weight: bold;">${name}</p>
                              </td>
                            </tr>
                          </table>

                          <!-- Email Row -->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 20px;">
                            <tr>
                              <td width="50" style="vertical-align: top; padding-right: 15px;">
                                <div style="background-color: #3b82f6; width: 40px; height: 40px; border-radius: 8px; text-align: center; line-height: 40px;">
                                  <span style="color: white; font-size: 18px;">📧</span>
                                </div>
                              </td>
                              <td style="vertical-align: top; border-bottom: 1px solid #e2e8f0; padding-bottom: 15px;">
                                <p style="color: #64748b; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">E-MAIL</p>
                                <p style="color: #1e293b; margin: 5px 0 0 0; font-size: 16px; font-weight: bold;">
                                  <a href="mailto:${email}" style="color: #3b82f6; text-decoration: none;">${email}</a>
                                </p>
                              </td>
                            </tr>
                          </table>

                          <!-- Subject Row -->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 20px;">
                            <tr>
                              <td width="50" style="vertical-align: top; padding-right: 15px;">
                                <div style="background-color: #3b82f6; width: 40px; height: 40px; border-radius: 8px; text-align: center; line-height: 40px;">
                                  <span style="color: white; font-size: 18px;">💬</span>
                                </div>
                              </td>
                              <td style="vertical-align: top; border-bottom: 1px solid #e2e8f0; padding-bottom: 15px;">
                                <p style="color: #64748b; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">BETREFF</p>
                                <p style="color: #1e293b; margin: 5px 0 0 0; font-size: 16px; font-weight: bold;">${subject}</p>
                              </td>
                            </tr>
                          </table>

                          <!-- Message Row -->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                              <td width="50" style="vertical-align: top; padding-right: 15px;">
                                <div style="background-color: #3b82f6; width: 40px; height: 40px; border-radius: 8px; text-align: center; line-height: 40px;">
                                  <span style="color: white; font-size: 18px;">📝</span>
                                </div>
                              </td>
                              <td style="vertical-align: top;">
                                <p style="color: #64748b; margin: 0; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">NACHRICHT</p>
                                <div style="background-color: #ffffff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin-top: 8px;">
                                  <p style="color: #374151; margin: 0; line-height: 1.6; font-size: 15px; white-space: pre-wrap;">${message}</p>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>

                    <!-- Action Button -->
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" style="padding: 20px 0;">
                          <table border="0" cellpadding="0" cellspacing="0">
                            <tr>
                              <td style="background-color: #3b82f6; border-radius: 6px;">
                                <a href="mailto:${email}" style="display: inline-block; color: #ffffff; padding: 15px 30px; text-decoration: none; font-weight: bold; font-size: 16px;">
                                  Antworten
                                </a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- Footer -->
                <tr>
                  <td style="background-color: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
                    <p style="color: #64748b; margin: 0; font-size: 14px;">
                      © ${new Date().getFullYear()} ${CONFIG.COMPANY_NAME} • Automatisch generiert
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;

    MailApp.sendEmail({
      to: CONFIG.ADMIN_EMAIL,
      subject: emailSubject,
      htmlBody: htmlBody,
      replyTo: email
    });

    return true;
  } catch (error) {
    console.error('Error sending admin email:', error);
    return false;
  }
}

/**
 * Send confirmation email to user
 */
function sendUserEmail(name, email, subject) {
  try {
    const emailSubject = CONFIG.USER_SUBJECT.replace('{company}', CONFIG.COMPANY_NAME);

    const htmlBody = `
     <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>Vielen Dank für Ihre Nachricht</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc;">
          <tr>
            <td align="center" style="padding: 20px 0;">
              <table border="0" cellpadding="0" cellspacing="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

                <!-- Header with Company Branding -->
                <tr>
                  <td style="background-color: #3b82f6; padding: 50px 30px; text-align: center; border-radius: 8px 8px 0 0;">
                    <!-- Company Logo -->
                    <table border="0" cellpadding="0" cellspacing="0" align="center" style="margin-bottom: 10px;">
                      <tr>
                        <td style="background-color: rgba(255,255,255,0.1); padding: 0px 10px 15px 10px; border-radius: 20px;">
                          <img src="https://firebasestorage.googleapis.com/v0/b/gainback-9023c.appspot.com/o/test%2FCONALYS%20-%20White.png?alt=media&token=e341d001-3d37-4b75-b211-79cb8038b14b" alt="${CONFIG.COMPANY_NAME} Logo" style="height: 60px; width: auto; display: block;" />
                        <p style="color: #e0f2fe; margin: 10px 0 0 0; font-size: 18px;">
                      Vertragsprüfungen. Einfach gemacht
                    </p>
                        </td>
                        
                      </tr>
                    </table>

                    <!--<h1 style="color: #ffffff; margin: 0; font-size: 32px; font-weight: bold;">-->
                    <!--  ${CONFIG.COMPANY_NAME}-->
                    <!--</h1>-->
                    <!--<p style="color: #e0f2fe; margin: 10px 0 0 0; font-size: 18px;">-->
                    <!--  Vertragsprüfungen. Einfach gemacht-->
                    <!--</p>-->
                  </td>
                </tr>

                <!-- Main Content -->
                <tr>
                  <td style="padding: 40px 30px; background-color: #ffffff;">

                    <!-- Welcome Message -->
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 40px;">
                      <tr>
                        <td align="center">
                          <!-- Success Icon -->
                          <!--<table border="0" cellpadding="0" cellspacing="0" align="center" style="margin-bottom: 25px;">-->
                          <!--  <tr>-->
                          <!--    <td style="background-color: rgba(34, 197, 94, 0.1); width: 80px; height: 80px; border-radius: 40px; border: 2px solid rgba(34, 197, 94, 0.3); text-align: center; line-height: 76px;">-->
                          <!--      <span style="font-size: 36px;">✅</span>-->
                          <!--    </td>-->
                          <!--  </tr>-->
                          <!--</table>-->

                          <h2 style="color: #1e293b; margin: 0 0 15px 0; font-size: 28px; font-weight: bold;">
                            Hallo ${name}!
                          </h2>
                          <p style="color: #64748b; margin: 0; font-size: 18px; line-height: 1.6;">
                            Ihre Nachricht wurde erfolgreich übermittelt
                          </p>
                        </td>
                      </tr>
                    </table>

                    <!-- Thank You Message -->
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; margin-bottom: 30px;">
                      <tr>
                        <td style="padding: 40px; text-align: center;">
                          <h3 style="color: #1e293b; margin: 0 0 20px 0; font-size: 22px; font-weight: bold;">
                            🎉 Vielen Dank für Ihr Interesse!
                          </h3>
                          <p style="color: #374151; margin: 0 0 25px 0; font-size: 16px; line-height: 1.7;">
                            Wir haben Ihre Anfrage erhalten und freuen uns über Ihr Interesse an ${CONFIG.COMPANY_NAME}.
                            Unser Team wird Ihre Nachricht sorgfältig prüfen und sich innerhalb von <strong style="color: #3b82f6;">48 Stunden</strong> bei Ihnen melden.
                          </p>

                          <!-- Submission Details -->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ffffff; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 25px 0;">
                            <tr>
                              <td style="padding: 25px;">
                                <h4 style="color: #1e293b; margin: 0 0 15px 0; font-size: 16px; font-weight: bold; text-align: center;">
                                  📋 Ihre Anfrage im Überblick
                                </h4>
                                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                  <tr>
                                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">
                                      <strong style="color: #1e293b;">Betreff:</strong> ${subject}
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">
                                      <strong style="color: #1e293b;">Gesendet am:</strong> ${new Date().toLocaleString('de-DE')}
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">
                                      <strong style="color: #1e293b;">Referenz-ID:</strong> #${Date.now().toString().slice(-6)}
                                    </td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>

                    <!-- Contact Info (Optional - Currently Commented) -->
                    <!--
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; margin-bottom: 30px;">
                      <tr>
                        <td style="padding: 30px; text-align: center;">
                          <h3 style="color: #1e293b; margin: 0 0 20px 0; font-size: 18px; font-weight: bold;">
                            💬 Haben Sie dringende Fragen?
                          </h3>
                          <p style="color: #64748b; margin: 0 0 20px 0; font-size: 15px; line-height: 1.6;">
                            Falls Sie nicht warten möchten, können Sie uns auch direkt kontaktieren:
                          </p>
                          <table border="0" cellpadding="0" cellspacing="0" align="center">
                            <tr>
                              <td style="background-color: #3b82f6; border-radius: 6px;">
                                <a href="mailto:${CONFIG.ADMIN_EMAIL}" style="display: inline-block; color: #ffffff; padding: 12px 25px; text-decoration: none; font-weight: bold; font-size: 14px;">
                                  📧 ${CONFIG.ADMIN_EMAIL}
                                </a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>

                    <!-- Website Link -->
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" style="padding: 20px 0;">
                          <table border="0" cellpadding="0" cellspacing="0">
                            <tr>
                              <td style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px;">
                                <a href="${CONFIG.COMPANY_WEBSITE}" style="display: inline-block; color: #3b82f6; padding: 15px 30px; text-decoration: none; font-weight: bold; font-size: 16px;">
                                  🌐 Website besuchen
                                </a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- Footer -->
                <tr>
                  <td style="background-color: #f8fafc; padding: 40px 30px; text-align: center; border-top: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
                    <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
                      © ${new Date().getFullYear()} ${CONFIG.COMPANY_NAME}. Alle Rechte vorbehalten.
                    </p>
                    <p style="color: #9ca3af; margin: 0; font-size: 12px;">
                      Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht direkt auf diese E-Mail.
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;

    MailApp.sendEmail({
      to: email,
      subject: emailSubject,
      htmlBody: htmlBody,
      replyTo: CONFIG.ADMIN_EMAIL
    });

    return true;
  } catch (error) {
    console.error('Error sending user email:', error);
    return false;
  }
}

/**
 * Test function to verify email sending works
 * Run this manually in Google Apps Script to test
 */
function testEmailSending() {
  console.log('Testing email sending...');
  console.log('Admin email configured as:', CONFIG.ADMIN_EMAIL);

  try {
    // Test admin email
    const adminResult = sendAdminEmail(
      'Test User',
      '<EMAIL>',
      'Test Subject',
      'This is a test message from Google Apps Script'
    );
    console.log('Admin email test result:', adminResult);

    // Test user email
    const userResult = sendUserEmail(
      'Test User',
      CONFIG.ADMIN_EMAIL, // Send to admin email for testing
      'Test Subject'
    );
    console.log('User email test result:', userResult);

    if (adminResult && userResult) {
      console.log('✅ Email sending test PASSED');
      return 'SUCCESS: Both emails sent successfully';
    } else {
      console.log('❌ Email sending test FAILED');
      return 'FAILED: One or both emails failed to send';
    }
  } catch (error) {
    console.error('Email test error:', error);
    return 'ERROR: ' + error.toString();
  }
}

/**
 * Test function for doPost
 */
function testDoPost() {
  // Create a mock POST request
  const e = {
    postData: {
      type: 'application/json',
      contents: JSON.stringify({
        name: "Test User",
        email: "<EMAIL>",
        subject: "Test Subject",
        message: "This is a test message",
        origin: "https://conalys.com"
      })
    }
  };

  // Call doPost with the mock data
  const result = doPost(e);
  Logger.log(result.getContent());
}

/**
 * Validate email format
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Create HTTP response with CORS headers
 */
function createResponse(statusCode, data) {
  const response = ContentService.createTextOutput(JSON.stringify(data));
  response.setMimeType(ContentService.MimeType.JSON);

  // In Google Apps Script, we can't set headers directly on the response object
  // Instead, we return an object with both content and headers
  return {
    response: response,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  };
}