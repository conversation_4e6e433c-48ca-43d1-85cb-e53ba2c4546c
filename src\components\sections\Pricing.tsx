"use client";
import { motion, useScroll, useTransform } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useParallax } from "../../hooks/use-parallax";

export default function Pricing() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, 0]);
  const parallaxRef = useParallax(10);

  const features = [
    "Alle Features inklusive",
    "Unbegrenzte Dokumente",
    "Premium Support",
    "Flexible Nutzung ohne langfristige Bindung",
    "Automatische Updates",
    "Prioritäts-Support",
  ];
  const handleClick = () => {
    window.location.href = "https://buy.stripe.com/8x23cva6d37r1rZcr243S00"; // your copied URL
  };
  return (
    <section id="pricing" className="relative py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" />

      {/* <div className="container mx-auto px-6 relative z-10"> */}
      <motion.div
        ref={ref}
        style={{ y }}
        className="container mx-auto px-6 relative z-10"
      >
        {/* <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        > */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <motion.h2
            ref={parallaxRef}
            className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"
          >
            Einfache Preisgestaltung
          </motion.h2>
          <p className="text-xl text-gray-400">
            Transparent und flexibel für Ihr Team
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative"
        >
          <div className="relative bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors">
            <div className="text-center mb-12">
              <div className="flex justify-center items-baseline mb-8">
                <span className="text-6xl font-bold">49.99€</span>
                <span className="text-xl text-gray-400 ml-2">/Monat</span>
              </div>
              <p className="text-gray-400 text-lg mb-8">
                Einfaches, transparentes Preismodell – ohne versteckte Kosten
              </p>
            </div>

            <div className="space-y-6 mb-12">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={inView ? { opacity: 1, x: 0 } : {}}
                  transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                  className="flex items-center"
                >
                  <div className="mr-4 w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-300">{feature}</span>
                </motion.div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleClick}
              className="w-full h-12 bg-white rounded-xl text-gray-900 font-medium hover:shadow-lg transition-all duration-300"
            >
              Jetzt starten
            </motion.button>
          </div>
        </motion.div>
        {/* </motion.div> */}
      </motion.div>
    </section>
  );
}
