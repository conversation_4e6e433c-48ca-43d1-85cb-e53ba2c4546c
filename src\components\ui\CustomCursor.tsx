import { useState, useEffect } from "react";
import { motion, useMotionValue, useSpring } from "framer-motion";

export default function CustomCursor() {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Motion values for smooth cursor tracking
  const cursorX = useMotionValue(0);
  const cursorY = useMotionValue(0);

  // Spring animations for smooth following effect
  const springConfig = { damping: 25, stiffness: 700, mass: 0.5 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  // Slower spring for the outer circle
  const outerSpringConfig = { damping: 25, stiffness: 150, mass: 0.8 };
  const outerX = useSpring(cursorX, outerSpringConfig);
  const outerY = useSpring(cursorY, outerSpringConfig);

  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => {
      setIsMobile(
        window.matchMedia("(hover: none) and (pointer: coarse)").matches
      );
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    const handleMouseMove = (e: MouseEvent) => {
      if (isMobile) return;
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);
      setIsVisible(true);
    };

    const handleMouseEnter = () => {
      if (isMobile) return;
      setIsVisible(true);
    };

    const handleMouseLeave = () => {
      if (isMobile) return;
      setIsVisible(false);
    };

    const handleMouseOver = (e: MouseEvent) => {
      if (isMobile) return;
      const target = e.target as HTMLElement;
      const isInteractive =
        target.matches(
          'a, button, [role="button"], input, textarea, select, [tabindex]'
        ) ||
        target.closest(
          'a, button, [role="button"], input, textarea, select, [tabindex]'
        );
      setIsHovering(!!isInteractive);
    };

    // Add event listeners
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseenter", handleMouseEnter);
    document.addEventListener("mouseleave", handleMouseLeave);
    document.addEventListener("mouseover", handleMouseOver);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseenter", handleMouseEnter);
      document.removeEventListener("mouseleave", handleMouseLeave);
      document.removeEventListener("mouseover", handleMouseOver);
      window.removeEventListener("resize", checkMobile);
    };
  }, [cursorX, cursorY, isMobile]);

  // Keep default cursor visible - no need to hide it
  useEffect(() => {
    // We keep the default cursor visible alongside our custom cursor
    return () => {
      // Cleanup if needed
    };
  }, []);

  // Don't render cursor on mobile devices
  if (isMobile || !isVisible) return null;

  return (
    <>
      {/* Outer Circle - Trailing effect */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9991]"
        style={{
          x: outerX,
          y: outerY,
        }}
        animate={{
          scale: isHovering ? 1.8 : 1,
          opacity: isVisible ? 0.8 : 0,
        }}
        transition={{
          scale: { duration: 0.3, ease: "easeOut" },
          opacity: { duration: 0.2 },
        }}
      >
        <div
          className="w-10 h-10 rounded-full border-2 border-blue-400/40 bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm"
          style={{
            transform: "translate(-50%, -50%)",
            boxShadow:
              "0 0 20px rgba(59, 130, 246, 0.3), inset 0 0 20px rgba(147, 51, 234, 0.2)",
          }}
        />
      </motion.div>

      {/* Inner Dot - Precise following */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9992]"
        style={{
          x: cursorXSpring,
          y: cursorYSpring,
        }}
        animate={{
          scale: isHovering ? 0.3 : 1,
          opacity: isVisible ? 1 : 0,
        }}
        transition={{
          scale: { duration: 0.2, ease: "easeOut" },
          opacity: { duration: 0.2 },
        }}
      >
        <motion.div
          className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"
          style={{
            transform: "translate(-50%, -50%)",
            boxShadow:
              "0 0 15px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.4)",
          }}
          animate={{
            boxShadow: [
              "0 0 15px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.4)",
              "0 0 20px rgba(59, 130, 246, 1), 0 0 40px rgba(147, 51, 234, 0.6)",
              "0 0 15px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.4)",
            ],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </motion.div>
    </>
  );
}
