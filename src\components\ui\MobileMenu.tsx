import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { navigateToSection } from "../../utils/navigation";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  logoUrl: string;
}

export default function MobileMenu({
  isOpen,
  onClose,
  logoUrl,
}: MobileMenuProps) {
  const menuItems = [
    { href: "#features", label: "Funktionen" },
    { href: "#pricing", label: "Preise" },
    { href: "#contact", label: "Kontakt" },
  ];

  const menuVariants = {
    closed: {
      opacity: 0,
      x: "100%",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 40,
      },
    },
    open: {
      opacity: 1,
      x: "0%",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 40,
      },
    },
  };

  const itemVariants = {
    closed: { x: 50, opacity: 0 },
    open: (i: number) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: i * 0.1,
        type: "spring",
        stiffness: 400,
        damping: 40,
      },
    }),
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40"
          />

          {/* Menu */}
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={menuVariants}
            className="fixed top-0 right-0 bottom-0 w-[300px] bg-gray-950/95 backdrop-blur-xl border-l border-white/10 z-50 p-6 overflow-y-auto"
          >
            {/* Logo and close button */}
            <div className="flex items-center justify-between mb-8">
              <img src={logoUrl} alt="CONALYS" className="h-12 w-auto" />
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="p-2 hover:bg-white/5 rounded-lg transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </motion.button>
            </div>

            {/* Menu items */}
            <nav className="space-y-2">
              {menuItems.map((item, i) => (
                <motion.a
                  key={item.href}
                  href={item.href}
                  custom={i}
                  variants={itemVariants}
                  onClick={onClose}
                  className="block w-full px-4 py-3 text-lg font-medium hover:bg-white/5 rounded-lg transition-colors"
                >
                  {item.label}
                </motion.a>
              ))}
              <motion.div
                custom={menuItems.length}
                variants={itemVariants}
                className="pt-4 mt-4 border-t border-white/10"
              >
                <button
                  onClick={() => {
                    navigateToSection("#pricing");
                    onClose();
                  }}
                  className="w-full h-11 px-6 rounded-lg bg-white text-gray-900 font-medium hover:shadow-lg hover:shadow-white/10 transition-all duration-300"
                >
                  Jetzt starten
                </button>
              </motion.div>
            </nav>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
