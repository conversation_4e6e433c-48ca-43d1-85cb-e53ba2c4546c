import { motion } from 'framer-motion';

interface LoadingScreenProps {
  logoUrl: string;
}

export default function LoadingScreen({ logoUrl }: LoadingScreenProps) {
  return (
    <div className="fixed inset-0 bg-gray-950 flex items-center justify-center">
      <div className="relative w-full max-w-sm mx-auto px-4">
        {/* Background blur effects */}
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-drift" />
        <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-drift-slow" />
        
        {/* Grid overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:32px_32px]" />

        {/* Content */}
        <div className="relative z-10 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="mb-4"
          >
            <img src={logoUrl} alt="CONALYS" className="h-16 w-auto" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mb-8"
          >
            <span className="text-sm tracking-[0.2em] text-gray-400 font-medium flex flex-col items-center">
              <span>VERTRAGSPRÜFUNGEN.</span>
              <span>EINFACH GEMACHT.</span>
            </span>
          </motion.div>

          <div className="flex justify-center gap-2">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                initial={{ scale: 0.8, opacity: 0.4 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: i * 0.2
                }}
                className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}