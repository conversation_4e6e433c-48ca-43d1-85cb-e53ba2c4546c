import { useParallax } from "@/hooks/use-parallax";
import { motion, useScroll, useTransform } from "framer-motion";
import { useInView } from "react-intersection-observer";

const features = [
  {
    title: "Inhouse Legal Teams",
    description: "Nie wieder E-Mail-Chaos oder widersprüchliche Versionen.",
    icon: "🎓",
    gradient: "from-green-500 to-emerald-500",
  },
  {
    title: "Anwaltskanzleien",
    description:
      "Professionelles Vertragsfeedback für Mandanten – klar dokumentiert.",
    icon: "⚖️",
    gradient: "from-purple-500 to-pink-500",
    // gradient: "from-purple-500 to-pink-500",
  },
  {
    title: "Unternehmen & Einkauf",
    description:
      "Mehr Klarheit bei komplexen Vertragsverhandlungen mit Lieferanten & Partnern.",
    icon: "🏢",

    gradient: "from-blue-500 to-cyan-500",
  },
  //   {
  //     title: "Einfach in Ihre Prozesse integrierbar",
  //     description: "<PERSON><PERSON>l-Wechsel. Kein neues System lernen.",
  //     icon: "💼",
  //     gradient: "from-orange-500 to-yellow-500",
  //   },
  //   {
  //     title: "Zurück zur Dokumentenansicht",
  //     description:
  //       "Nach der Überprüfung können Tabelleninhalte wieder in ein normales Word-Dokument umgewandelt werden – inklusive aller Änderungen.",
  //     icon: "📄",
  //     gradient: "from-red-500 to-orange-500",
  //   },
  //   {
  //     title: "Plattformübergreifende Nutzung",
  //     description:
  //       "Funktioniert reibungslos sowohl in der Desktop- als auch in der Online-Version von Microsoft Word.",
  //     icon: "💻",
  //     gradient: "from-blue-500 to-indigo-500",
  //   },
];

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: [0.48, 0.15, 0.25, 0.96],
    },
  }),
};

const WhoisConalys = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, 0]);
  const parallaxRef = useParallax(25);

  return (
    <section
      id="whoisconalys"
      className="relative py-32 overflow-hidden bg-gray-950"
    >
      {/* <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px]" /> */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" />

      <motion.div
        ref={ref}
        style={{ y }}
        className="container mx-auto px-6 relative z-10"
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <motion.h2
            ref={parallaxRef}
            className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"
          >
            Für wen ist Conalys gedacht?
          </motion.h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Entdecken Sie, wie Conalys speziell auf Ihre Bedürfnisse
            zugeschnitten ist.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              custom={index}
              variants={cardVariants}
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              className="relative bg-gray-800 rounded-xl p-8 border border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-4xl mb-4 mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-center mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                {feature.title}
              </h3>
              <p className="text-gray-400 text-center">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  );
};

export default WhoisConalys;
