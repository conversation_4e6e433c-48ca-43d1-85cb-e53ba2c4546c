import { motion } from 'framer-motion';

export default function CursorDemo() {
  return (
    <div className="min-h-screen bg-gray-950 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <motion.h1 
          className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Custom Cursor Animation Demo
        </motion.h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Interactive Buttons */}
          <motion.div 
            className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-6 border border-gray-700/50"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <h3 className="text-xl font-semibold mb-4">Interactive Elements</h3>
            <div className="space-y-4">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors">
                Hover Me
              </button>
              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg transition-colors">
                Click Me
              </button>
              <a 
                href="#" 
                className="block w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-lg text-center hover:from-blue-600 hover:to-purple-600 transition-all"
              >
                Link Element
              </a>
            </div>
          </motion.div>

          {/* Form Elements */}
          <motion.div 
            className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-6 border border-gray-700/50"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <h3 className="text-xl font-semibold mb-4">Form Elements</h3>
            <div className="space-y-4">
              <input 
                type="text" 
                placeholder="Text Input"
                className="w-full bg-gray-700 text-white py-2 px-4 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none transition-colors"
              />
              <textarea 
                placeholder="Textarea"
                rows={3}
                className="w-full bg-gray-700 text-white py-2 px-4 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none transition-colors resize-none"
              />
              <select className="w-full bg-gray-700 text-white py-2 px-4 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none transition-colors">
                <option>Select Option</option>
                <option>Option 1</option>
                <option>Option 2</option>
              </select>
            </div>
          </motion.div>

          {/* Text Content */}
          <motion.div 
            className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-6 border border-gray-700/50"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <h3 className="text-xl font-semibold mb-4">Text Content</h3>
            <p className="text-gray-300 mb-4">
              Move your cursor around this area to see the smooth trailing effect. 
              The cursor follows your mouse with a beautiful animation.
            </p>
            <p className="text-gray-400 text-sm">
              Notice how the cursor changes when you hover over interactive elements 
              and creates ripple effects when you click.
            </p>
          </motion.div>
        </div>

        {/* Large Interactive Area */}
        <motion.div 
          className="mt-12 bg-gradient-to-br from-gray-800/40 to-gray-900/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 text-center"
          whileHover={{ scale: 1.01 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-2xl font-semibold mb-6">Large Interactive Area</h3>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            This is a large area where you can move your cursor around to see the full effect 
            of the custom cursor animation. Try clicking in different areas to see the ripple effects.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <motion.button
                key={i}
                className="bg-blue-600/20 hover:bg-blue-600/40 border border-blue-500/30 text-blue-300 py-4 px-6 rounded-xl transition-all"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Button {i}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Instructions */}
        <motion.div 
          className="mt-8 text-center text-gray-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <p className="text-sm">
            💡 Tip: Try hovering over different elements and clicking to see various cursor effects!
          </p>
        </motion.div>
      </div>
    </div>
  );
}
