import { motion } from "framer-motion";

const CTASec = () => {
  return (
    <section className="relative py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" />

      <div className="container max-w-4xl mx-auto ">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div>
            <h2 className="text-3xl font-bold mb-8 text-center">
              Bereit für die nächste Stufe der Vertragsbearbeitung?
            </h2>
          </div>
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0 "
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full sm:w-auto h-12 px-8 rounded-xl bg-white text-gray-900 font-medium hover:shadow-lg hover:shadow-white/10 transition-all duration-300"
            >
              Jetzt starten
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default CTASec;
