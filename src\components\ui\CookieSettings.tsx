import { useState } from 'react';
import { motion } from 'framer-motion';
import { useCookieConsent } from '@/hooks/use-cookie-consent';
import CookiePreferencesModal from './CookiePreferencesModal';

interface CookieSettingsProps {
  className?: string;
  variant?: 'button' | 'link';
  children?: React.ReactNode;
}

export default function CookieSettings({ 
  className = '', 
  variant = 'link',
  children 
}: CookieSettingsProps) {
  const [showModal, setShowModal] = useState(false);
  const { hasConsented, consentData } = useCookieConsent();

  const handleClick = () => {
    setShowModal(true);
  };

  const baseClasses = variant === 'button' 
    ? 'px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors'
    : 'text-gray-400 hover:text-white transition-colors';

  return (
    <>
      <motion.button
        onClick={handleClick}
        className={`${baseClasses} ${className}`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {children || (
          <span className="flex items-center gap-2">
            <span>🍪</span>
            Cookie-Einstellungen
          </span>
        )}
      </motion.button>

      {/* Show consent status for debugging */}
      {process.env.NODE_ENV === 'development' && hasConsented && (
        <div className="text-xs text-gray-500 mt-1">
          Consent: {consentData?.consentDate ? new Date(consentData.consentDate).toLocaleDateString() : 'Unknown'}
        </div>
      )}

      <CookiePreferencesModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
}
