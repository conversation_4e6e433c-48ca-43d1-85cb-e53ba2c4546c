{"name": "vite-react", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview", "lint": "eslint ."}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@iconify/react": "^5.2.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-visually-hidden": "^1.1.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.7.4", "input-otp": "^1.4.2", "lucide-react": "^0.471.1", "react": "^18.3.1", "react-day-picker": "^9.5.0", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.1.1", "recharts": "^2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2", "vite-plugin-pwa": "^0.21.1"}, "devDependencies": {"@stylistic/eslint-plugin": "^2.12.1", "@stylistic/eslint-plugin-js": "^2.12.1", "@stylistic/eslint-plugin-jsx": "^2.12.1", "@stylistic/eslint-plugin-ts": "^2.12.1", "@types/prismjs": "^1.26.5", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.14.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3", "vite": "^6.0.11"}}