import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useCookieConsent } from "@/hooks/use-cookie-consent";
import CookiePreferencesModal from "./CookiePreferencesModal";

/**
 * <PERSON>ie Consent Popup Modal
 *
 * Shows as a centered modal overlay when user hasn't given consent yet.
 * Only appears when cookies haven't been accepted/declined.
 * Blocks interaction with the page until user makes a choice.
 *
 * IMPORTANT: Does NOT show on privacy-related pages (/privacy, /terms, /imprint)
 * to avoid interrupting users who are reading privacy information.
 */
export default function CookieConsentBanner() {
  const {
    showBanner = false,
    acceptAll,
    acceptNecessary,
    isLoading,
  } = useCookieConsent();

  const [showPreferences, setShowPreferences] = useState(false);

  // Don't render if loading or banner shouldn't be shown
  if (isLoading || !showBanner) {
    return null;
  }

  return (
    <>
      <AnimatePresence>
        {showBanner && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
          >
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />

            {/* Modal */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="relative w-full max-w-lg"
            >
              <div className="bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 md:p-8 shadow-2xl">
                {/* Background gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl" />

                <div className="relative text-center">
                  {/* Cookie Icon */}
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <span className="text-3xl">🍪</span>
                  </motion.div>

                  {/* Content */}
                  <h3 className="text-2xl font-semibold text-white mb-4">
                    Cookie-Einstellungen
                  </h3>
                  <p className="text-gray-300 text-sm leading-relaxed mb-6">
                    Wir verwenden Cookies, um Ihnen die beste Erfahrung auf
                    unserer Website zu bieten. Einige sind notwendig für das
                    Funktionieren der Seite, während andere uns helfen, die
                    Website zu verbessern und Ihnen relevante Inhalte zu zeigen.
                  </p>

                  {/* Privacy Policy Link */}
                  <motion.a
                    href="/privacy"
                    target="_blank"
                    className="inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 text-sm mb-8 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Mehr in unserer Datenschutzerklärung
                    <svg
                      className="w-3 h-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </motion.a>

                  {/* Actions */}
                  <div className="space-y-3">
                    {/* Accept All - Primary Action */}
                    <motion.button
                      onClick={acceptAll}
                      className="w-full px-6 py-4 text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-xl transition-all duration-200 font-medium shadow-lg shadow-blue-500/25"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Alle Cookies akzeptieren
                    </motion.button>

                    <div className="flex gap-3">
                      {/* Accept Necessary */}
                      <motion.button
                        onClick={acceptNecessary}
                        className="flex-1 px-4 py-3 text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600 rounded-xl transition-all duration-200 text-sm font-medium"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Nur Notwendige
                      </motion.button>

                      {/* Preferences Button */}
                      <motion.button
                        onClick={() => setShowPreferences(true)}
                        className="flex-1 px-4 py-3 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 rounded-xl transition-all duration-200 text-sm font-medium"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Einstellungen
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cookie Preferences Modal */}
      <CookiePreferencesModal
        isOpen={showPreferences}
        onClose={() => setShowPreferences(false)}
      />
    </>
  );
}
